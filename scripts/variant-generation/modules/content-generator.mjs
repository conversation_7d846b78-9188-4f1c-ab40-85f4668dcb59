/**
 * Content generation utilities for variant pages
 */
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { ToolTypeManager } from './tool-type-manager.mjs'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 获取原始工具的基础内容
export function getBaseToolContent(toolType) {
  try {
    // 尝试加载英文翻译文件作为基础内容
    const translationPath = path.join(
      __dirname,
      `../../../src/i18n/locales/en/${toolType}.json`,
    )
    if (fs.existsSync(translationPath)) {
      const translation = JSON.parse(fs.readFileSync(translationPath, 'utf8'))

      // 根据工具类型使用不同的内容结构
      let baseContent

      if (toolType === 'oc-maker') {
        // OC Maker 特定的内容结构
        baseContent = {
          // 基础信息
          toolName:
            translation.title || translation.header?.title || 'OC Maker',
          intro: translation.whatIs?.description || '',

          // 主要内容区域
          header: {
            title: translation.header?.title || 'OC Maker',
            subtitle: translation.header?.subtitle || '',
          },

          // 各个section - OC Maker 结构
          sections: {
            whatIs: {
              title: translation.whatIs?.title || 'What is OC Maker',
              description: translation.whatIs?.description || '',
            },
            howToUse: {
              title:
                translation.howToUse?.title ||
                translation.howItWorks?.title ||
                'How to Use OC Maker',
              subtitle:
                translation.howToUse?.description ||
                translation.howItWorks?.description ||
                '',
              steps: translation.howItWorks?.steps
                ? Object.values(translation.howItWorks.steps)
                : [],
            },
            whyUse: {
              title: translation.features?.title || 'Why Use OC Maker',
              subtitle: '',
              features: translation.features?.features
                ? Object.values(translation.features.features)
                : [],
            },
          },

          // 样式和应用 - OC Maker 没有这些，使用空数组
          styles: [],
          applications: [],

          // 提示和FAQ
          tips: [],
          faq: translation.faq
            ? Object.keys(translation.faq)
                .filter(key => key.startsWith('question'))
                .map((questionKey, index) => {
                  const answerKey = `answer${questionKey.replace('question', '')}`
                  return {
                    question: translation.faq[questionKey],
                    answer: translation.faq[answerKey],
                  }
                })
            : [],

          // SEO信息
          seo: {
            title: translation.title || '',
            description: translation.meta?.description || '',
            keywords: translation.meta?.keywords || '',
          },
        }
      } else if (toolType === 'ai-comic-generator') {
        // AI Comic Generator 特定的内容结构
        baseContent = {
          // 基础信息
          toolName:
            translation.title ||
            translation.header?.title ||
            'AI Comic Generator',
          intro: translation.whatIs?.description || '',

          // 主要内容区域
          header: {
            title: translation.header?.title || 'AI Comic Generator',
            subtitle: translation.header?.subtitle || '',
          },

          // 各个section - AI Comic Generator 结构
          sections: {
            whatIs: {
              title: translation.whatIs?.title || 'What is AI Comic Generator',
              description: translation.whatIs?.description || '',
            },
            howToUse: {
              title:
                translation.howToUse?.title || 'How to Create Comics with AI',
              subtitle: translation.howToUse?.subtitle || '',
              steps: translation.howToUse?.steps
                ? Object.values(translation.howToUse.steps)
                : [],
            },
            whyUse: {
              title: translation.whyUse?.title || 'Why Use AI Comic Generator',
              subtitle: translation.whyUse?.subtitle || '',
              features: translation.whyUse?.features
                ? Object.values(translation.whyUse.features)
                : [],
            },
            styles: {
              title: translation.styles?.title || 'Comic Styles Available',
              items: translation.styles?.items || [],
            },
            applications: {
              title:
                translation.applications?.title || 'Comic Generator Use Cases',
              items: translation.applications?.items || [],
            },
          },

          // 样式和应用 - AI Comic Generator 有这些
          styles: translation.styles?.items || [],
          applications: translation.applications?.items || [],

          // 提示和FAQ
          tips: [],
          faq: translation.faq
            ? Object.keys(translation.faq)
                .filter(key => key.startsWith('question'))
                .map((questionKey, index) => {
                  const answerKey = `answer${questionKey.replace('question', '')}`
                  return {
                    question: translation.faq[questionKey],
                    answer: translation.faq[answerKey],
                  }
                })
            : [],

          // SEO信息
          seo: {
            title: translation.title || '',
            description: translation.meta?.description || '',
            keywords: translation.meta?.keywords || '',
          },
        }
      } else {
        // AI Anime Generator 和其他工具的原有结构
        baseContent = {
          // 基础信息
          toolName:
            translation.head?.title ||
            translation.title ||
            toolType.replace('-', ' '),
          intro: translation.intro?.description1 || '',

          // 主要内容区域
          header: {
            title:
              translation.content?.header?.title ||
              translation.header?.title ||
              translation.head?.title ||
              '',
            subtitle:
              translation.content?.header?.subtitle ||
              translation.header?.subtitle ||
              translation.head?.description ||
              '',
          },

          // 各个section
          sections: {
            whatIs: {
              title:
                translation.headings?.whatIs ||
                translation.sections?.whatIs?.title ||
                '',
              description:
                translation.content?.sections?.whatIs?.description ||
                translation.sections?.whatIs?.description ||
                '',
            },
            howToUse: {
              title:
                translation.headings?.howToUse ||
                translation.sections?.howToUse?.title ||
                '',
              subtitle:
                translation.content?.sections?.howToUse?.subtitle ||
                translation.sections?.howToUse?.subtitle ||
                '',
              steps:
                translation.content?.sections?.howToUse?.steps ||
                translation.sections?.howToUse?.steps ||
                translation.steps ||
                [],
            },
            whyUse: {
              title:
                translation.headings?.whyUse ||
                translation.sections?.whyUse?.title ||
                '',
              subtitle:
                translation.content?.sections?.whyUse?.subtitle ||
                translation.sections?.whyUse?.subtitle ||
                '',
              features:
                translation.content?.sections?.whyUse?.features ||
                translation.sections?.whyUse?.features ||
                translation.features ||
                [],
            },
            faq: {
              title:
                translation.headings?.faq ||
                translation.sections?.faq?.title ||
                translation.faqSection?.title ||
                `FAQ`,
              description:
                translation.content?.sections?.faq?.description ||
                translation.sections?.faq?.description ||
                translation.faqSection?.description ||
                `Everything you need to know`,
            },
          },

          // 样式和应用
          styles:
            translation.styles ||
            translation.content?.sections?.styles?.items ||
            [],
          applications:
            translation.applications ||
            translation.content?.sections?.applications?.items ||
            [],

          // 提示和FAQ
          tips: translation.tips?.items || translation.tips || [],
          faq: translation.faq?.items || translation.faq || [],

          // SEO信息
          seo: {
            title: translation.head?.title || translation.title || '',
            description:
              translation.head?.description ||
              translation.meta?.description ||
              '',
            keywords:
              translation.head?.keywords || translation.meta?.keywords || '',
          },
        }
      }

      console.log(`✅ 成功加载 ${toolType} 的基础内容`)
      return baseContent
    }
  } catch (error) {
    console.warn(`⚠️ 无法加载${toolType}的基础内容:`, error.message)
  }

  // 返回空对象作为fallback
  return {
    toolName: toolType.replace('-', ' '),
    intro: '',
    header: { title: '', subtitle: '' },
    sections: { whatIs: {}, howToUse: {}, whyUse: {} },
    styles: [],
    applications: [],
    tips: [],
    faq: [],
    seo: { title: '', description: '', keywords: '' },
  }
}

export const ContentGenerator = {
  getBaseToolContent
}
