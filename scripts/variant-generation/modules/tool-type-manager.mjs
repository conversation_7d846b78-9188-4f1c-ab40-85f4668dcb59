/**
 * Tool type configuration and management
 */
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { ToolLoader } from './tool-loader.mjs'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 工具类型配置 map (保留作为fallback)
const LEGACY_TOOL_TYPE_CONFIG = {
  'ai-anime-generator': {
    name: 'AI Anime Generator',
    description: 'art generation',
    targetAudience: 'Artists, designers, content creators, and hobbyists interested in creating art',
    contentType: 'art',
    placeholderTemplate: 'A scene in {keyword} style',
    buttonTemplate: 'Create {keyword} Art Free',
    functionalDescription: 'AI {keyword} Generator: an AI tool that creates {keyword} style artwork from text descriptions. First input your text prompt describing the {keyword} scene or character you want, then select the {keyword} art style from various templates, then click Generate Art, and you will get a high-quality {keyword} style artwork.',
    stepTemplates: {
      step1: { title: 'Describe Your Vision', content: 'Explain how to write a good prompt for \'{keyword}\' style. Use an example. Match the detail level of the few-shot example.' },
      step2: { title: 'Customize Your Creation', content: 'Explain available customization options relevant to the art style. Follow example format.' },
      step3: { title: 'Generate Your Art', content: 'Explain the generation process. Match example style.' },
      step4: { title: 'Download and Share', content: 'Encourage users to download and share their \'{keyword}\' creations. Follow example tone.' }
    },
    seoStandard: 'Follow this quality standard for art generation: \'Generate stunning {keyword} scenes with our AI. Bring your creative visions to life in seconds. Try it free!\'',
    keywordFocus: '5-8 core keywords focusing on AI-powered art generation. Include \'AI\' in 2-3 key terms since users specifically search for AI tools. Primary keyword once, then add genuine search terms like \'AI art generator\', \'AI anime generator\', \'anime AI art\', plus relevant non-AI terms users actually type. Prioritize real search intent over keyword stuffing.',
    tips: {
      tip1: 'Tip 1 for \'{keyword}\' art - make it specific and actionable like the example tips.',
      tip2: 'Tip 2 for \'{keyword}\' art - follow the format and depth of the example.',
      tip3: 'Tip 3 for \'{keyword}\' art - match the practical value of the example tips.'
    },
    faq: {
      q1: 'What is {keyword} and how does it work?',
      q2: 'How to create the best {keyword} with AI?',
      q3: 'Is {keyword} {toolName} free to use?',
      q4: 'What makes {keyword} unique compared to other styles?',
      q5: 'Can I use {keyword} creations commercially?'
    },
    skipImages: false,
    ratioOverride: null
  },
  'oc-maker': {
    name: 'OC Maker',
    description: 'character creation',
    targetAudience: 'Artists, writers, game developers, and content creators interested in creating original characters (OCs) for their stories, games, comics, and animations',
    contentType: 'character',
    placeholderTemplate: 'A {keyword} character with blue hair and magical powers',
    buttonTemplate: 'Create {keyword} Characters Free',
    functionalDescription: 'AI {keyword} Character Creator: an AI tool that generates original {keyword} characters from text descriptions. First input your character description including appearance, personality, and traits for your {keyword} character, then select the {keyword} character style from various templates, then click Generate Character, and you will get a unique {keyword} character design.',
    stepTemplates: {
      step1: { title: 'Describe Your Character', content: 'Explain how to describe a {keyword} character\'s appearance, personality, and traits. Use specific examples. Match the detail level of the few-shot example.' },
      step2: { title: 'Customize Character Details', content: 'Explain available customization options for {keyword} characters like appearance, clothing, accessories. Follow example format.' },
      step3: { title: 'Generate Your Character', content: 'Explain the character generation process. Match example style.' },
      step4: { title: 'Finalize and Use', content: 'Encourage users to finalize their {keyword} character design and use it in their projects. Follow example tone.' }
    },
    seoStandard: 'Follow this quality standard for character creation: \'Create unique {keyword} characters with AI. Design original characters with custom appearances and personalities. Try it free!\'',
    keywordFocus: '5-8 core keywords focusing on AI-powered character creation. Include \'AI\' in 2-3 key terms since users specifically search for AI tools. Primary keyword once, then add genuine search terms like \'AI character creator\', \'OC maker\', \'character generator\', plus relevant non-AI terms users actually type. Prioritize real search intent over keyword stuffing.',
    tips: {
      tip1: 'Tip 1 for \'{keyword}\' character creation - make it specific and actionable like the example tips.',
      tip2: 'Tip 2 for \'{keyword}\' character design - follow the format and depth of the example.',
      tip3: 'Tip 3 for \'{keyword}\' character development - match the practical value of the example tips.'
    },
    faq: {
      q1: 'What is {keyword} OC Maker and how does it work?',
      q2: 'How to create the best {keyword} characters with AI?',
      q3: 'Is {keyword} OC Maker free to use?',
      q4: 'What makes {keyword} characters unique?',
      q5: 'Can I use {keyword} characters commercially?'
    },
    skipImages: false,
    ratioOverride: 'portrait-2-3'
  },
  'ai-comic-generator': {
    name: 'AI Comic Generator',
    description: 'comic creation',
    targetAudience: 'Comic creators, storytellers, writers, and content creators interested in creating comics, graphic novels, and visual narratives',
    contentType: 'comic',
    placeholderTemplate: 'A thrilling {keyword} adventure story with heroes and villains',
    buttonTemplate: 'Create {keyword} Comics Free',
    functionalDescription: 'AI {keyword} Comic Generator: an AI tool that creates {keyword} style comics from story descriptions. First input your story plot, characters, and dialogue for your {keyword} comic, then select the {keyword} comic style from various panel layouts and art styles, then click Generate Comic, and you will get a complete {keyword} style comic with panels and speech bubbles.',
    stepTemplates: {
      step1: { title: 'Write Your Story', content: 'Explain how to write a compelling {keyword} comic story with characters, plot, and dialogue. Use specific examples. Match the detail level of the few-shot example.' },
      step2: { title: 'Choose Comic Style', content: 'Explain available {keyword} comic style options like panel layouts, art styles, and visual effects. Follow example format.' },
      step3: { title: 'Generate Your Comic', content: 'Explain the comic generation process with panels, speech bubbles, and visual storytelling. Match example style.' },
      step4: { title: 'Download and Share', content: 'Encourage users to download their {keyword} comic and share it with others. Follow example tone.' }
    },
    seoStandard: 'Follow this quality standard for comic creation: \'Create amazing {keyword} comics with AI. Generate engaging visual stories and narratives instantly. Try it free!\'',
    keywordFocus: '5-8 core keywords focusing on AI-powered comic creation. Include \'AI\' in 2-3 key terms since users specifically search for AI tools. Primary keyword once, then add genuine search terms like \'AI comic generator\', \'comic creator\', \'comic maker\', plus relevant non-AI terms users actually type. Prioritize real search intent over keyword stuffing.',
    tips: {
      tip1: 'Tip 1 for \'{keyword}\' comic creation - make it specific and actionable like the example tips.',
      tip2: 'Tip 2 for \'{keyword}\' comic storytelling - follow the format and depth of the example.',
      tip3: 'Tip 3 for \'{keyword}\' comic publishing - match the practical value of the example tips.'
    },
    faq: {
      q1: 'What is {keyword} Comic Generator and how does it work?',
      q2: 'How to create the best {keyword} comics with AI?',
      q3: 'Is {keyword} Comic Generator free to use?',
      q4: 'What makes {keyword} comics unique?',
      q5: 'Can I use {keyword} comics commercially?'
    },
    skipImages: true,
    ratioOverride: null
  },
  'playground': {
    name: 'AI Style Transfer',
    description: 'style transformation',
    targetAudience: 'Artists, designers, content creators, and hobbyists interested in transforming photos into different artistic styles',
    contentType: 'style',
    placeholderTemplate: 'Transform your photo into {keyword} style',
    buttonTemplate: 'Transform to {keyword} Free',
    functionalDescription: 'AI {keyword} Generator: an AI tool that converts your photos to {keyword} style. First upload your photo or image, then select the {keyword} template, then click Convert to {keyword}, and you will get a photo transformed into {keyword} style.',
    stepTemplates: {
      step1: { title: 'Upload Your Photo', content: 'Explain how to upload and prepare photos for {keyword} style transformation. Use specific examples. Match the detail level of the few-shot example.' },
      step2: { title: 'Select {keyword} Style', content: 'Explain the {keyword} style characteristics and how to select it from available options. Follow example format.' },
      step3: { title: 'Generate Your Art', content: 'Explain the AI transformation process for {keyword} style. Match example style.' },
      step4: { title: 'Download and Share', content: 'Encourage users to download and share their {keyword} style creations. Follow example tone.' }
    },
    seoStandard: 'Follow this quality standard for style transformation: \'Transform photos into stunning {keyword} style with AI. Create unique artistic variations instantly. Try it free!\'',
    keywordFocus: '5-8 core keywords focusing on AI-powered style transformation. Include \'AI\' in 2-3 key terms since users specifically search for AI tools. Primary keyword once, then add genuine search terms like \'AI style transfer\', \'photo to {keyword}\', \'{keyword} filter\', plus relevant non-AI terms users actually type. Prioritize real search intent over keyword stuffing.',
    tips: {
      tip1: 'Tip 1 for \'{keyword}\' style transformation - make it specific and actionable like the example tips.',
      tip2: 'Tip 2 for \'{keyword}\' style creation - follow the format and depth of the example.',
      tip3: 'Tip 3 for \'{keyword}\' artistic effects - match the practical value of the example tips.'
    },
    faq: {
      q1: 'What is {keyword} style transformation and how does it work?',
      q2: 'How to create the best {keyword} style effects with AI?',
      q3: 'Is {keyword} style transformation free to use?',
      q4: 'What makes {keyword} style unique compared to other filters?',
      q5: 'Can I use {keyword} style creations commercially?'
    },
    skipImages: false,
    ratioOverride: null
  }
}

// 获取工具类型配置 (新版本 - 使用动态加载)
export async function getToolTypeConfig(toolType) {
  try {
    // 首先尝试从新的工具配置加载
    const config = await ToolLoader.loadToolConfig(toolType)
    if (config) {
      return config
    }
  } catch (error) {
    console.warn(`⚠️ 无法从新配置加载 ${toolType}，使用legacy配置`)
  }

  // 回退到legacy配置
  return LEGACY_TOOL_TYPE_CONFIG[toolType] || LEGACY_TOOL_TYPE_CONFIG['ai-anime-generator']
}

// 获取工具类型配置 (同步版本 - 用于向后兼容)
export function getToolTypeConfigSync(toolType) {
  return LEGACY_TOOL_TYPE_CONFIG[toolType] || LEGACY_TOOL_TYPE_CONFIG['ai-anime-generator']
}

// 支持的工具类型 - 从index.json文件动态获取并合并配置
export function getSupportedToolTypes() {
  try {
    const indexPath = path.join(__dirname, '../../../src/data/variants/index.json')
    const indexData = JSON.parse(fs.readFileSync(indexPath, 'utf8'))
    const toolTypes = {}

    indexData.tools.forEach(tool => {
      const config = LEGACY_TOOL_TYPE_CONFIG[tool.key] || LEGACY_TOOL_TYPE_CONFIG['ai-anime-generator'] // 默认回退
      
      toolTypes[tool.key] = {
        name: tool.name,
        description: config.description,
        category: config.contentType === 'character' ? 'Character Generation' : 
                 config.contentType === 'comic' ? 'Comic Generation' : 'Art Generation',
        baseTemplate: tool.key,
        config: config
      }
    })

    return toolTypes
  } catch (error) {
    console.warn('⚠️ 无法读取variants/index.json，使用默认配置')
    // 回退到默认配置
    const defaultTools = {}
    Object.keys(LEGACY_TOOL_TYPE_CONFIG).forEach(toolKey => {
      const config = LEGACY_TOOL_TYPE_CONFIG[toolKey]
      defaultTools[toolKey] = {
        name: config.name,
        description: config.description,
        category: config.contentType === 'character' ? 'Character Generation' : 
                 config.contentType === 'comic' ? 'Comic Generation' : 'Art Generation',
        baseTemplate: toolKey,
        config: config
      }
    })
    return defaultTools
  }
}

export const ToolTypeManager = {
  getToolTypeConfig,
  getToolTypeConfigSync,
  getSupportedToolTypes,
  LEGACY_TOOL_TYPE_CONFIG,
  // 向后兼容
  TOOL_TYPE_CONFIG: LEGACY_TOOL_TYPE_CONFIG
}
