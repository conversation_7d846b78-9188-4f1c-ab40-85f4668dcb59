/**
 * File management utilities for variant pages
 */
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { ConfigManager } from './config-manager.mjs'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 将关键字转换为合适的slug格式（用于文件路径和URL）
export function keywordToSlug(keyword) {
  return keyword
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-') // 将非字母数字字符替换为连字符
    .replace(/^-+|-+$/g, '') // 移除开头和结尾的连字符
}

// 更新variant数据
export function updateVariantPages(toolType, keyword, pageContent, examples) {
  console.log('🔄 正在更新variant数据...')
  console.log(`📝 pageContent存在: ${!!pageContent}`)
  console.log(`🖼️ examples数量: ${examples.length}`)

  // 如果没有pageContent，尝试加载现有数据或创建基本结构
  if (!pageContent) {
    console.log('⚠️ pageContent为空，尝试加载现有数据...')

    // 检查是否有现有的variant文件
    const existingPage = checkExistingPage(toolType, keyword)
    if (existingPage.exists && existingPage.data) {
      console.log('📄 使用现有页面数据')
      pageContent = existingPage.data
    } else {
      console.log('📝 创建基本页面结构（仅用于保存图片）')
      // 创建最基本的结构来保存图片
      pageContent = {
        seo: {},
        placeholderText: `Generate ${keyword} style artwork`,
        content: {
          header: {
            title: keyword,
            subtitle: `Generate ${keyword} style artwork with AI`,
          },
        },
        originalKeyword: keyword,
      }
    }
  }

  // 合并新旧examples，保留原有图片
  const existingExamples = pageContent.content?.examples || []
  const mergedExamples = [...existingExamples, ...examples]

  console.log(
    `📊 examples合并: 原有${existingExamples.length}张 + 新增${examples.length}张 = 总计${mergedExamples.length}张`,
  )

  // 检测是否为SEO-only模式（新增examples为0且与原有examples相同）
  const isSeoOnlyMode =
    examples.length > 0 &&
    examples.length === existingExamples.length &&
    JSON.stringify(examples) === JSON.stringify(existingExamples)

  if (isSeoOnlyMode) {
    console.log('🔍 检测到SEO-only模式，保持examples不变')
  }

  // 准备内容（包含生成的图片）
  const contentWithExamples = {
    seo: pageContent.seo || {},
    placeholderText:
      pageContent.placeholderText || `Generate ${keyword} style artwork`,
    content: {
      ...pageContent.content,
      examples: mergedExamples,
    },
    // 保存原始关键字用于显示
    originalKeyword: keyword,
  }

  console.log('📦 准备保存的内容结构:')
  console.log(
    `- seo: ${
      !!contentWithExamples.seo &&
      Object.keys(contentWithExamples.seo).length > 0
    }`,
  )
  console.log(`- placeholderText: ${!!contentWithExamples.placeholderText}`)
  console.log(`- content: ${!!contentWithExamples.content}`)
  console.log(`- content.header: ${!!contentWithExamples.content?.header}`)
  console.log(
    `- examples: ${contentWithExamples.content.examples?.length || 0}张`,
  )

  // 直接使用分离文件结构
  updateVariantFile(toolType, keyword, contentWithExamples)
}

// 更新variant文件
export function updateVariantFile(toolType, keyword, contentWithExamples) {
  // 工具文件夹路径
  const toolDir = path.join(ConfigManager.VARIANTS_DIR, toolType)

  // 确保工具目录存在
  if (!fs.existsSync(toolDir)) {
    fs.mkdirSync(toolDir, { recursive: true })
  }

  // 使用规范化的关键字作为文件名
  const keywordSlug = keywordToSlug(keyword)
  const variantFilePath = path.join(toolDir, `${keywordSlug}.json`)

  // 创建variant文件内容
  const variantData = {
    seo: contentWithExamples.seo,
    placeholderText: contentWithExamples.placeholderText,
    content: contentWithExamples.content,
    originalKeyword: contentWithExamples.originalKeyword,
  }

  // 保存variant文件
  fs.writeFileSync(variantFilePath, JSON.stringify(variantData, null, 2))
  console.log(`✅ ${toolType}/${keywordSlug}.json 已更新`)
}

// 检查页面是否已存在
export function checkExistingPage(toolType, keyword) {
  const keywordSlug = keywordToSlug(keyword)
  const variantFilePath = path.join(
    __dirname,
    `../../../src/data/variants/${toolType}/${keywordSlug}.json`,
  )

  if (!fs.existsSync(variantFilePath)) {
    return { exists: false }
  }

  try {
    const existingData = JSON.parse(fs.readFileSync(variantFilePath, 'utf8'))
    const hasContent =
      existingData.content && Object.keys(existingData.content).length > 0
    const hasImages =
      existingData.content?.examples && existingData.content.examples.length > 0

    return {
      exists: true,
      hasContent,
      hasImages,
      imageCount: hasImages ? existingData.content.examples.length : 0,
      data: existingData,
    }
  } catch (error) {
    console.warn(`⚠️  读取现有文件失败: ${error.message}`)
    return { exists: false }
  }
}

export const FileManager = {
  keywordToSlug,
  updateVariantPages,
  updateVariantFile,
  checkExistingPage
}
