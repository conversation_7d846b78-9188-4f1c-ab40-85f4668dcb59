/**
 * 工具配置加载器 - 按tool type组织的配置管理
 */
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 工具配置缓存
const toolConfigCache = new Map()

/**
 * 动态加载工具配置
 * @param {string} toolType - 工具类型
 * @returns {Object} 工具配置对象
 */
export async function loadToolConfig(toolType) {
  // 检查缓存
  if (toolConfigCache.has(toolType)) {
    return toolConfigCache.get(toolType)
  }

  try {
    const configPath = path.join(__dirname, `../tool-configs/${toolType}/config.mjs`)
    
    if (!fs.existsSync(configPath)) {
      console.warn(`⚠️ 工具配置文件不存在: ${configPath}`)
      return null
    }

    // 动态导入配置
    const configModule = await import(`file://${configPath}?t=${Date.now()}`)
    const config = configModule.default || configModule[Object.keys(configModule)[0]]
    
    // 缓存配置
    toolConfigCache.set(toolType, config)
    
    return config
  } catch (error) {
    console.error(`❌ 加载工具配置失败 (${toolType}):`, error.message)
    return null
  }
}

/**
 * 获取所有支持的工具类型
 * @returns {Array} 工具类型列表
 */
export function getSupportedToolTypes() {
  const toolConfigsDir = path.join(__dirname, '../tool-configs')
  
  if (!fs.existsSync(toolConfigsDir)) {
    return []
  }

  return fs.readdirSync(toolConfigsDir, { withFileTypes: true })
    .filter(dirent => dirent.isDirectory())
    .map(dirent => dirent.name)
}

/**
 * 加载工具的示例数据
 * @param {string} toolType - 工具类型
 * @returns {Object|null} 示例数据
 */
export async function loadToolExamples(toolType) {
  try {
    const examplesPath = path.join(__dirname, `../tool-configs/${toolType}/examples.json`)
    
    if (!fs.existsSync(examplesPath)) {
      return null
    }

    const examples = JSON.parse(fs.readFileSync(examplesPath, 'utf8'))
    return examples
  } catch (error) {
    console.warn(`⚠️ 加载工具示例失败 (${toolType}):`, error.message)
    return null
  }
}

/**
 * 加载工具的模板数据
 * @param {string} toolType - 工具类型
 * @returns {Object|null} 模板数据
 */
export async function loadToolTemplates(toolType) {
  try {
    const templatesPath = path.join(__dirname, `../tool-configs/${toolType}/templates.json`)
    
    if (!fs.existsSync(templatesPath)) {
      return null
    }

    const templates = JSON.parse(fs.readFileSync(templatesPath, 'utf8'))
    return templates
  } catch (error) {
    console.warn(`⚠️ 加载工具模板失败 (${toolType}):`, error.message)
    return null
  }
}

/**
 * 加载工具的提示词配置
 * @param {string} toolType - 工具类型
 * @returns {Object|null} 提示词配置
 */
export async function loadToolPrompts(toolType) {
  try {
    const promptsPath = path.join(__dirname, `../tool-configs/${toolType}/prompts.mjs`)
    
    if (!fs.existsSync(promptsPath)) {
      return null
    }

    const promptsModule = await import(`file://${promptsPath}?t=${Date.now()}`)
    return promptsModule.default || promptsModule
  } catch (error) {
    console.warn(`⚠️ 加载工具提示词失败 (${toolType}):`, error.message)
    return null
  }
}

/**
 * 清除工具配置缓存
 * @param {string} toolType - 工具类型，如果不提供则清除所有缓存
 */
export function clearToolConfigCache(toolType = null) {
  if (toolType) {
    toolConfigCache.delete(toolType)
  } else {
    toolConfigCache.clear()
  }
}

export const ToolLoader = {
  loadToolConfig,
  getSupportedToolTypes,
  loadToolExamples,
  loadToolTemplates,
  loadToolPrompts,
  clearToolConfigCache
}
