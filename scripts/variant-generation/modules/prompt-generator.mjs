/**
 * 新的AI Prompt生成器 - 整合到工具配置系统
 */

import { ToolLoader } from './tool-loader.mjs'

// 通用风格映射 - 用于向后兼容
const LEGACY_STYLE_MAPPING = {
  // Genshin variants
  'genshin-oc-maker': 'Genshin Impact',
  'genshin-oc-generator': 'Genshin Impact',
  'genshin-impact-oc-maker': 'Genshin Impact',
  'random-genshin-character-generator': 'Genshin Impact',

  // Anime franchises
  'jujutsu-kaisen-oc-maker': '<PERSON><PERSON><PERSON>',
  'jjk-oc-maker': '<PERSON><PERSON><PERSON>',
  'naruto-oc-maker': 'Naruto',
  'one-piece-oc-maker': 'One Piece',
  'one-piece-characters-generator': 'One Piece',
  'demon-slayer-oc-maker': 'Demon Slayer',
  'dragon-ball-oc-maker': 'Dragon Ball',
  'aot-oc-maker': 'Attack on Titan',
  'attack-on-titan-oc-maker': 'Attack on Titan',
  'my-hero-academia-oc-maker': 'My Hero Academia',
  'spy-x-family-oc-maker': 'Spy x Family',

  // Other franchises
  'pokemon-oc-maker': 'Pokemon',
  'my-little-pony-oc-maker': 'My Little Pony',
  'mlp-oc-maker': 'My Little Pony',
  'pony-oc-maker': 'My Little Pony',
  'sonic-oc-maker': 'Sonic the Hedgehog',
  'disney-oc-maker': 'Disney',
  'league-of-legends-oc-maker': 'League of Legends',
  'marvel-oc-maker': 'Marvel',
  'ai-marvel-character-generator': 'Marvel',

  // Character types
  'vampire-oc-maker': 'vampire',
  'anime-oc-maker': 'anime',
  'anime-character-generator': 'anime',
  'perchance-ai-character-generator': 'anime',
  'nsfw-character-creator': 'NSFW character',
  'nsfw-oc-maker': 'NSFW character',

  // MLP
  'mlp-ai-generator': 'My Little Pony',
  'my-little-pony-ai-generator': 'My Little Pony',
}

/**
 * 获取风格关键词
 */
function getStyleKeyword(variantKey) {
  const normalizedKey = variantKey.toLowerCase().replace(/\s+/g, '-')
  
  if (LEGACY_STYLE_MAPPING[normalizedKey]) {
    return LEGACY_STYLE_MAPPING[normalizedKey]
  }

  // Fallback: extract style from variant name
  return variantKey
    .replace(/-/g, ' ')
    .replace(
      /\b(oc maker|oc generator|character generator|generator|ai|creator|maker|builder|from text free)\b/gi,
      '',
    )
    .trim() || 'anime character'
}

/**
 * 生成AI prompts
 */
export async function generatePrompts(
  variantKey,
  variantData,
  count = 20,
  config = {},
  toolType = 'ai-anime-generator',
) {
  try {
    console.log(`🎨 Generating ${count} prompts for ${toolType}: ${variantKey}`)

    // 获取风格关键词
    const styleKeyword = getStyleKeyword(variantKey)
    const theme = styleKeyword
    console.log(`🎯 Style mapping: "${variantKey}" -> "${theme}"`)

    // 尝试从新的工具配置系统加载prompt模板
    let aiPrompt
    try {
      const prompts = await ToolLoader.loadToolPrompts(toolType)
      if (prompts && prompts.generateAIPrompt) {
        aiPrompt = prompts.generateAIPrompt(theme, count, variantKey)
        console.log('✅ 使用新的工具配置系统生成prompt')
      }
    } catch (error) {
      console.log('⚠️ 新配置系统不可用，使用legacy prompt生成')
    }

    // 如果新系统不可用，使用legacy prompt生成
    if (!aiPrompt) {
      aiPrompt = generateLegacyPrompt(variantKey, theme, count, toolType)
    }

    // 调用AI API
    const response = await callAI(aiPrompt, config)

    if (!response || response.trim().length === 0) {
      throw new Error('Empty AI response')
    }

    // 处理AI返回的prompts
    const promptLines = response
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .map(line => {
        // 去掉序号
        return line.replace(/^\d+[.\)\-]\s*/, '')
      })

    const results = promptLines.slice(0, count)
    console.log(`✅ Generated ${results.length} prompts via AI`)
    return results

  } catch (error) {
    console.warn('⚠️ AI prompt generation failed:', error.message)
    throw error
  }
}

/**
 * Legacy prompt生成 - 向后兼容
 */
function generateLegacyPrompt(variantKey, theme, count, toolType) {
  if (variantKey.toLowerCase().includes('pfp')) {
    return generatePFPPrompt(theme, count)
  } else if (variantKey.toLowerCase().includes('game-asset')) {
    return generateGameAssetPrompt(theme, count)
  } else if (toolType === 'oc-maker') {
    return generateOCMakerPrompt(theme, count)
  } else {
    return generateGenericPrompt(theme, count)
  }
}

/**
 * 生成PFP prompt
 */
function generatePFPPrompt(theme, count) {
  return `You are an expert Danbooru prompt generator specializing in creating anime Profile Pictures (PFPs). Your task is to generate a list of high-quality, comma-separated Danbooru tags optimized for AI anime models.

THEME: "${theme}" for an Anime PFP

**PFP PROMPT STRUCTURE - FOLLOW EXACTLY:**
1. **Subject**: Start with one character, like \`1girl\` or \`1boy\`, and use \`solo\` for a single subject.
2. **Composition and View (CRITICAL FOR PFP)**: For profile pictures, use \`portrait\`, \`close-up\` or \`upper body\`, and ALWAYS include \`looking at viewer\` for frontal face view.
3. **Character Details**: Describe physical features, clothing, and accessories. Be specific.
4. **Pose and Emotion**: Define the character's expression.
5. **Background and Setting**: For PFPs, use \`simple background\` or abstract background.
6. **Art Style**: Include tags for a specific aesthetic.
7. **Quality and Scoring**: End with quality tags like \`masterpiece\`, \`best quality\`.

**FORMATTING REQUIREMENTS:**
- Output ONLY the comma-separated tag prompts.
- Each prompt on a new line, no numbering.
- Use precise Danbooru tags with spaces for multi-word tags.

Now, generate ${count} unique anime PFP prompts for the theme "${theme}".`
}

/**
 * 生成Game Asset prompt
 */
function generateGameAssetPrompt(theme, count) {
  return `You are an expert Danbooru prompt generator specializing in creating **AI Game Assets**. Your task is to generate a list of high-quality, comma-separated Danbooru tags optimized for creating game-ready assets.

THEME: "${theme}" (Game Asset)

**GAME ASSET PROMPT STRUCTURE:**
1. **Subject & Asset Type:** The main focus (character, item, environment, UI element).
2. **Asset Details:** Specifics about the subject.
3. **Style & Medium (CRITICAL):** Define the asset's artistic style.
4. **Composition & Lighting:** Camera angles, framing, and lighting.

**FORMATTING REQUIREMENTS:**
- Output ONLY the comma-separated tag prompts.
- Each prompt on a new line, no numbering.
- Use precise Danbooru tags with spaces for multi-word tags.

Now, generate ${count} unique game asset prompts for the theme "${theme}".`
}

/**
 * 生成OC Maker prompt
 */
function generateOCMakerPrompt(theme, count) {
  return `You are an expert anime character design prompt generator specializing in Danbooru tag-based prompts for creating original characters (OCs).

THEME: "${theme}"
TOOL TYPE: OC Maker (Original Character Creator)

**CRITICAL PROMPT STRUCTURE:**
1. **Subject Count:** Start with \`1girl\`, \`1boy\`, or appropriate character type
2. **Character Traits:** Hair color, eye color, age, personality traits
3. **Standard Supplements:** Always end with "single character, upper body, looking at viewer, anime style, simple background, white background, best quality, masterpiece"

**FORMATTING REQUIREMENTS:**
- Output ONLY the comma-separated tag prompts
- Each prompt on a new line, no numbering
- Use precise Danbooru tags

Now, generate ${count} original character prompts for the theme "${theme}".`
}

/**
 * 生成通用prompt
 */
function generateGenericPrompt(theme, count) {
  return `You are an expert Danbooru prompt generator. Your task is to generate a list of high-quality, comma-separated Danbooru tags based on a given theme.

THEME: "${theme}"

**INSTRUCTIONS:**
1. Analyze the theme and generate ${count} unique, high-quality Danbooru prompts
2. Each prompt must be a single line of comma-separated tags
3. Ensure variety in characters, styles, and moods
4. Every prompt must include quality tags like \`masterpiece, best quality\`

**FORMATTING REQUIREMENTS:**
- Output ONLY the prompts
- Each prompt on a new line, no numbering

Now, generate ${count} prompts for the theme "${theme}".`
}

/**
 * AI API调用
 */
async function callAI(prompt, config) {
  try {
    const requestBody = {
      prompt: prompt,
      noNeedLogin: true,
    }

    const response = await fetch(`${config.apiBaseUrl}/api/generateText`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody),
    })

    if (!response.ok) {
      throw new Error(`API failed: ${response.status}`)
    }

    const result = await response.json()
    return result || ''
  } catch (error) {
    console.warn('⚠️ AI API call failed:', error.message)
    throw error
  }
}

/**
 * 验证prompts
 */
export function validatePrompts(prompts) {
  if (!Array.isArray(prompts)) return []

  return prompts.filter(prompt => {
    return typeof prompt === 'string' && prompt.trim().length > 10
  })
}

export default {
  generatePrompts,
  validatePrompts,
}
