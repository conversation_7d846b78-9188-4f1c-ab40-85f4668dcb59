#!/usr/bin/env node
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

// Import modules
import { ImageUtils } from './modules/image-utils.mjs'
import { ConfigManager } from './modules/config-manager.mjs'
import { ContentGenerator } from './modules/content-generator.mjs'
import { ImageGenerator } from './modules/image-generator.mjs'
import { FileManager } from './modules/file-manager.mjs'
import { ToolTypeManager } from './modules/tool-type-manager.mjs'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)



// Use module functions
const setupConfig = ConfigManager.setupConfig

// Use module constants
const IMAGE_RATIOS = ImageUtils.IMAGE_RATIOS

// Use module constants and functions
const getBaseToolContent = ContentGenerator.getBaseToolContent
const getToolTypeConfig = ToolTypeManager.getToolTypeConfigSync
const getSupportedToolTypes = ToolTypeManager.getSupportedToolTypes
const generateImages = ImageGenerator.generateImages
const generateImagePrompts = ImageGenerator.generateImagePrompts
const updateVariantPages = FileManager.updateVariantPages
const checkExistingPage = FileManager.checkExistingPage
const keywordToSlug = FileManager.keywordToSlug




// 生成随机页面结构，避免内容牧场（基于keyword生成一致的随机种子）
function generateRandomPageStructure(keyword, toolInfo) {
  // 基于keyword生成一致的随机种子
  let seed = 0
  for (let i = 0; i < keyword.length; i++) {
    seed += keyword.charCodeAt(i)
  }

  // 基于种子的伪随机函数
  let randomSeed = Math.abs(seed)
  const seededRandom = () => {
    randomSeed = (randomSeed * 9301 + 49297) % 233280
    return randomSeed / 233280
  }

  // 定义所有可用的版块
  const allSections = [
    'whatIs',
    'howToUse',
    'whyUse',
    'examples',
    'applications',
    'tips',
  ]

  // Examples必须在前两个位置（基于种子决定）
  const examplesPosition = seededRandom() < 0.5 ? 0 : 1 // 0 或 1

  // 移除examples，准备其他版块
  const otherSections = allSections.filter(section => section !== 'examples')

  // 使用种子随机打乱其他版块
  const shuffledSections = [...otherSections].sort(() => seededRandom() - 0.5)

  // 构建最终结构
  const structure = []

  // 插入Examples到指定位置
  if (toolInfo.baseTemplate !== 'ai-comic-generator') {
    if (examplesPosition === 0) {
      structure.push('examples')
      structure.push(...shuffledSections)
    } else {
      structure.push(shuffledSections[0])
      structure.push('examples')
      structure.push(...shuffledSections.slice(1))
    }
  } else {
    structure.push(...shuffledSections)
  }

  // 添加FAQ和CTA到最后
  structure.push('faq')
  structure.push('cta')

  console.log(`📋 为 "${keyword}" 生成固定页面结构: ${structure.join(' → ')}`)

  // 返回结构和对应的标题 - 使用固定格式
  // 根据关键词生成工具名称
  const toolName = `${keyword.charAt(0).toUpperCase() + keyword.slice(1)} AI Generator`

  return {
    structure,
    sectionTitles: {
      whatIs: `What is ${toolName}?`,
      howToUse: `How to Use The ${toolName}`,
      whyUse: `Why Use The ${toolName}`,
      examples: `${toolName} Examples`,
      applications: `${toolName} Applications and Use Cases`,
      tips: `Pro Tips for ${toolName} Creation`,
      faq: `${toolName} FAQ`,
      cta: `Start Creating ${keyword} Art Today`,
    },
  }
}

// 生成页面内容的AI prompt
function generateContentPrompt(toolType, keyword, toolInfo) {
  const baseContent = getBaseToolContent(toolType)
  const pageStructure = generateRandomPageStructure(keyword, toolInfo)

  // 加载原始工具的英文翻译文件作为few-shot example
  let fewShotExample = null
  try {
    const examplePath = path.join(
      __dirname,
      `../../src/i18n/locales/en/${toolType}.json`,
    )
    if (fs.existsSync(examplePath)) {
      fewShotExample = JSON.parse(fs.readFileSync(examplePath, 'utf8'))
      console.log(`✅ 加载few-shot示例: ${toolType}.json`)
    }
  } catch (error) {
    console.warn(`⚠️ 无法加载few-shot示例: ${error.message}`)
  }

  // 根据工具类型生成不同的few-shot prompt
  let fewShotPrompt = ''

  if (fewShotExample) {
    if (toolType === 'oc-maker') {
      // OC Maker 特定的few-shot结构
      fewShotPrompt = `

**IMPORTANT: Few-Shot Example (Use this as a template for quality and structure)**

Here's a high-quality example of how the original "${toolInfo.name}" content is structured. Use this as a template for style, depth, and quality when creating the "${keyword}" variation:

\`\`\`json
{
  "seo": {
    "title": "${fewShotExample.title || `${toolInfo.name}`}",
    "description": "${fewShotExample.meta?.description || ''}",
    "keywords": "${fewShotExample.meta?.keywords || ''}"
  },
  "placeholderText": "${fewShotExample.characterAppearance?.placeholder || 'Describe your character...'}",
  "header": {
    "title": "${fewShotExample.header?.title || toolInfo.name}",
    "subtitle": "${fewShotExample.header?.subtitle || ''}"
  },
  "sections": {
    "whatIs": {
      "title": "${fewShotExample.whatIs?.title || 'What is OC Maker'}",
      "description": "${fewShotExample.whatIs?.description || 'Example of detailed, engaging description style to follow...'}"
    },
    "howToUse": {
      "title": "${fewShotExample.howItWorks?.title ? fewShotExample.howItWorks.title.replace('OC', keyword) : fewShotExample.howToUse?.title || 'How to Use'}",
      "subtitle": "${fewShotExample.howToUse?.description || fewShotExample.howItWorks?.description || ''}",
      "steps": ${JSON.stringify(
        fewShotExample.howItWorks?.steps
          ? Object.values(fewShotExample.howItWorks.steps).slice(0, 4)
          : [
              {
                title: 'Example Step',
                content: 'Example content style to follow',
              },
            ],
        null,
        8,
      )}
    },
    "whyUse": {
      "title": "${fewShotExample.features?.title || 'Why Use This Tool'}",
      "subtitle": "",
      "features": ${JSON.stringify(
        fewShotExample.features?.features
          ? Object.values(fewShotExample.features.features).slice(0, 4)
          : [
              {
                title: 'Example Feature',
                content: 'Example feature description style',
              },
            ],
        null,
        8,
      )}
    }
  },
  "faq": ${JSON.stringify(
    fewShotExample.faq
      ? Object.keys(fewShotExample.faq)
          .filter(key => key.startsWith('question'))
          .slice(0, 5)
          .map(questionKey => {
            const answerKey = `answer${questionKey.replace('question', '')}`
            return {
              question: fewShotExample.faq[questionKey],
              answer: fewShotExample.faq[answerKey],
            }
          })
      : [
          {
            question: 'Example question format',
            answer: 'Example answer style and depth',
          },
        ],
    null,
    4,
  )}
}
\`\`\`

**KEY QUALITY INDICATORS TO MATCH:**
- Content depth and detail level from the example above
- Professional, engaging tone and writing style focused on character creation
- Specific, actionable information for OC/character design rather than generic descriptions
- Natural keyword integration without keyword stuffing
- Comprehensive coverage of character creation topics with real value for users`
    } else if (toolType === 'ai-comic-generator') {
      // AI Comic Generator 特定的few-shot结构
      fewShotPrompt = `

**IMPORTANT: Few-Shot Example (Use this as a template for quality and structure)**

Here's a high-quality example of how the original "${toolInfo.name}" content is structured. Use this as a template for style, depth, and quality when creating the "${keyword}" variation:

\`\`\`json
{
  "seo": {
    "title": "${fewShotExample.title || `${toolInfo.name}`}",
    "description": "${fewShotExample.meta?.description || ''}",
    "keywords": "${fewShotExample.meta?.keywords || ''}"
  },
  "placeholderText": "${fewShotExample.placeholderText || 'Describe your comic story...'}",
  "header": {
    "title": "${fewShotExample.header?.title || toolInfo.name}",
    "subtitle": "${fewShotExample.header?.subtitle || ''}"
  },
  "sections": {
    "whatIs": {
      "title": "${fewShotExample.whatIs?.title || 'What is AI Comic Generator'}",
      "description": "${fewShotExample.whatIs?.description || 'Example of detailed, engaging description style to follow...'}"
    },
    "howToUse": {
      "title": "${fewShotExample.howToUse?.title || 'How to Create Comics with AI'}",
      "subtitle": "${fewShotExample.howToUse?.subtitle || ''}",
      "steps": ${JSON.stringify(
        fewShotExample.howToUse?.steps
          ? Object.values(fewShotExample.howToUse.steps).slice(0, 4)
          : [
              {
                title: 'Example Step',
                content: 'Example content style to follow',
              },
            ],
        null,
        8,
      )}
    },
    "whyUse": {
      "title": "${fewShotExample.whyUse?.title || 'Why Use AI Comic Generator'}",
      "subtitle": "${fewShotExample.whyUse?.subtitle || ''}",
      "features": ${JSON.stringify(
        fewShotExample.whyUse?.features
          ? Object.values(fewShotExample.whyUse.features).slice(0, 4)
          : [
              {
                title: 'Example Feature',
                content: 'Example feature description style',
              },
            ],
        null,
        8,
      )}
    },
    "styles": {
      "title": "${fewShotExample.styles?.title || 'Comic Styles Available'}",
      "items": ${JSON.stringify(
        fewShotExample.styles?.items?.slice(0, 4) || [
          {
            title: 'Example Style',
            content: 'Example style description',
          },
        ],
        null,
        8,
      )}
    },
    "applications": {
      "title": "${fewShotExample.applications?.title || 'Comic Generator Use Cases'}",
      "items": ${JSON.stringify(
        fewShotExample.applications?.items?.slice(0, 4) || [
          {
            title: 'Example Application',
            content: 'Example application description',
          },
        ],
        null,
        8,
      )}
    }
  },
  "faq": ${JSON.stringify(
    fewShotExample.faq
      ? Object.keys(fewShotExample.faq)
          .filter(key => key.startsWith('question'))
          .slice(0, 5)
          .map(questionKey => {
            const answerKey = `answer${questionKey.replace('question', '')}`
            return {
              question: fewShotExample.faq[questionKey],
              answer: fewShotExample.faq[answerKey],
            }
          })
      : [
          {
            question: 'Example question format',
            answer: 'Example answer style and depth',
          },
        ],
    null,
    4,
  )}
}
\`\`\`

**KEY QUALITY INDICATORS TO MATCH:**
- Content depth and detail level from the example above
- Professional, engaging tone and writing style focused on comic creation
- Specific, actionable information for comic generation rather than generic descriptions
- Natural keyword integration without keyword stuffing
- Comprehensive coverage of comic creation topics with real value for users`
    } else if (toolType === 'playground') {
      // Playground 特定的few-shot结构
      fewShotPrompt = `

**IMPORTANT: Few-Shot Example (Use this as a template for quality and structure)**

Here's a high-quality example of how the original "${toolInfo.name}" content is structured. Use this as a template for style, depth, and quality when creating the "${keyword}" variation:

\`\`\`json
{
  "seo": {
    "title": "${fewShotExample.meta?.title || fewShotExample.title || `${toolInfo.name}`}",
    "description": "${fewShotExample.meta?.description || fewShotExample.hero?.description || ''}",
    "keywords": "${fewShotExample.meta?.keywords || ''}"
  },
  "placeholderText": "Transform your photo into ${keyword} style",
  "header": {
    "title": "${fewShotExample.hero?.title || toolInfo.name}",
    "subtitle": "${fewShotExample.hero?.description || ''}"
  },
  "sections": {
    "whatIs": {
      "title": "What is ${keyword} Style Transfer?",
      "description": "Example of detailed, engaging description style to follow for style transformation..."
    },
    "howToUse": {
      "title": "${fewShotExample.howItWorks?.title || 'How to Transform Photos'}",
      "subtitle": "${fewShotExample.howItWorks?.description || ''}",
      "steps": ${JSON.stringify(
        fewShotExample.howItWorks?.steps
          ? Object.values(fewShotExample.howItWorks.steps).slice(0, 4)
          : [
              {
                title: 'Upload Your Photo',
                content: 'Example content style for photo upload step',
              },
              {
                title: 'Select Style',
                content: 'Example content style for style selection step',
              },
              {
                title: 'Generate Art',
                content: 'Example content style for generation step',
              },
              {
                title: 'Download Result',
                content: 'Example content style for download step',
              },
            ],
        null,
        8,
      )}
    },
    "whyUse": {
      "title": "${fewShotExample.benefits?.title || 'Why Use AI Style Transfer'}",
      "subtitle": "",
      "features": ${JSON.stringify(
        fewShotExample.benefits?.features
          ? Object.values(fewShotExample.benefits.features).slice(0, 6)
          : [
              {
                title: '🎨 Example Feature 1',
                content: 'Example feature description style',
              },
              {
                title: '⚡ Example Feature 2',
                content: 'Example feature description style',
              },
              {
                title: '🖼️ Example Feature 3',
                content: 'Example feature description style',
              },
              {
                title: '💫 Example Feature 4',
                content: 'Example feature description style',
              },
              {
                title: '🎯 Example Feature 5',
                content: 'Example feature description style',
              },
              {
                title: '🚀 Example Feature 6',
                content: 'Example feature description style',
              },
            ],
        null,
        8,
      )}
    }
  },
  "faq": ${JSON.stringify(
    fewShotExample.faq
      ? Object.keys(fewShotExample.faq)
          .filter(key => key.startsWith('question'))
          .slice(0, 5)
          .map(questionKey => {
            const answerKey = `answer${questionKey.replace('question', '')}`
            return {
              question: fewShotExample.faq[questionKey],
              answer: fewShotExample.faq[answerKey],
            }
          })
      : [
          {
            question: 'Example question format',
            answer: 'Example answer style and depth',
          },
        ],
    null,
    4,
  )}
}
\`\`\`

**KEY QUALITY INDICATORS TO MATCH:**
- Content depth and detail level from the example above
- Professional, engaging tone and writing style focused on style transformation
- Specific, actionable information for photo-to-style conversion rather than generic descriptions
- Natural keyword integration without keyword stuffing
- Comprehensive coverage of style transformation topics with real value for users`
    } else {
      // AI Anime Generator 和其他工具的原有结构
      fewShotPrompt = `

**IMPORTANT: Few-Shot Example (Use this as a template for quality and structure)**

Here's a high-quality example of how the original "${toolInfo.name}" content is structured. Use this as a template for style, depth, and quality when creating the "${keyword}" variation:

\`\`\`json
{
  "seo": {
    "title": "${fewShotExample.title || fewShotExample.meta?.title || `${toolInfo.name}`}",
    "description": "${fewShotExample.meta?.description || fewShotExample.header?.subtitle || ''}",
    "keywords": "${fewShotExample.meta?.keywords || ''}"
  },
  "intro": {
    "description1": "${fewShotExample.intro?.description1 || ''}"
  },
  "header": {
    "title": "${fewShotExample.header?.title || toolInfo.name}",
    "subtitle": "${fewShotExample.header?.subtitle || ''}"
  },
  "sections": {
    "whatIs": {
      "title": "${fewShotExample.headings?.whatIs || ''}",
      "description": "Example of detailed, engaging description style to follow..."
    },
    "howToUse": {
      "title": "${fewShotExample.headings?.howToUse || ''}",
      "subtitle": "${fewShotExample.howToUse?.subtitle || ''}",
      "steps": ${JSON.stringify(
        fewShotExample.howToUse?.steps?.slice(0, 4) || [
          { title: 'Example Step', content: 'Example content style to follow' },
        ],
        null,
        8,
      )}
    },
    "whyUse": {
      "title": "${fewShotExample.headings?.whyUse || ''}",
      "subtitle": "${fewShotExample.whyUse?.subtitle || ''}",
      "features": ${JSON.stringify(
        fewShotExample.whyUse?.features?.slice(0, 4) || [
          {
            title: 'Example Feature',
            content: 'Example feature description style',
          },
        ],
        null,
        8,
      )}
    },
    "applications": {
      "title": "${fewShotExample.headings?.applications || 'Applications'}",
      "items": ${JSON.stringify(
        fewShotExample.applications?.slice(0, 4) || [
          {
            title: 'Example Application',
            content: 'Example application description',
          },
        ],
        null,
        8,
      )}
    },
    "tips": {
      "title": "${fewShotExample.headings?.tips || fewShotExample.tips?.title || 'Tips'}",
      "items": ${JSON.stringify(
        fewShotExample.tips?.items?.slice(0, 3) ||
          fewShotExample.tips?.slice(0, 3) || ['Example tip format and style'],
        null,
        8,
      )}
    }
  },
  "faq": ${JSON.stringify(
    fewShotExample.faq?.items?.slice(0, 5) ||
      fewShotExample.faq?.slice(0, 5) || [
        {
          question: 'Example question format',
          answer: 'Example answer style and depth',
        },
      ],
    null,
    4,
  )},
  "closing": {
    "description": "${fewShotExample.closing?.description || ''}",
    "buttonText": "${fewShotExample.closing?.buttonText || ''}"
  }
}
\`\`\`

**KEY QUALITY INDICATORS TO MATCH:**
- Content depth and detail level from the example above
- Professional, engaging tone and writing style
- Specific, actionable information rather than generic descriptions
- Natural keyword integration without keyword stuffing
- Comprehensive coverage of topics with real value for users`
    }
  }

  // 根据工具类型生成不同的主要prompt
  const toolConfig = getToolTypeConfig(toolType)
  const toolDescription = toolConfig.description
  const targetAudience = toolConfig.targetAudience

  return `
Act as an SEO content strategist and expert copywriter. Your task is to generate the full JSON content for a new webpage on our platform, Komiko.

The new page is a specialized tool page for "${keyword} ${
    toolInfo.name
  }". It's a variation of our main "${toolInfo.name}" tool.

Here is the information you need:

**1. Page Goal:**
- To attract users searching for "${keyword}" related ${toolDescription}.
- To rank for the primary keyword: "${keyword} ${toolInfo.name}".
- To convince users to try our AI tool.

**2. Target Audience:**
- ${targetAudience}.
- They are looking for a powerful, fast, and easy-to-use AI ${toolConfig.contentType === 'character' ? 'character creator' : toolConfig.contentType === 'comic' ? 'comic generator' : 'art generator'}.

**3. Functional Description Template:**
Use this functional description as a guide to ensure accurate content generation that reflects actual functionality:
"${toolConfig.functionalDescription.replace('{keyword}', keyword)}"

This template should inform your content creation to ensure it accurately describes what the tool actually does, rather than relying on AI imagination.

${fewShotPrompt}

**4. Content Requirements:**
- **CRITICAL**: Match the quality, depth, and style shown in the few-shot example above
- **CRITICAL**: Adapt the example content specifically for "${keyword}" while maintaining the same level of detail and professionalism
- **CRITICAL**: Use the functional description template above to ensure content accurately reflects actual tool functionality
- **CRITICAL**: For SEO title, use ONLY the keyword "${keyword}" - do not add any suffix or additional text
- **CRITICAL**: For subtitle, focus ONLY on "${keyword}" capabilities - do NOT mention "${toolInfo.name}" or any other tool names
- **CRITICAL**: For whyUse features titles, start each title with a relevant emoji that matches the feature's purpose
- Use the exact same JSON structure and field names as shown in the example
- Ensure all content is specifically tailored to "${keyword}" rather than generic
- Include natural keyword integration throughout all sections
- Provide actionable, valuable information that users will find helpful

**5. JSON Output Structure:**

Please generate a JSON object with the following structure, using the few-shot example as your quality benchmark:

{
  "seo": {
    "title": "${keyword}",
    "description": "Craft a meta description (150-160 characters) for '${keyword}'. It must be a clear, compelling summary reflecting user search intent. Start by stating what the tool does, highlight a benefit, and end with a strong call-to-action. Integrate the primary keyword naturally. ${toolConfig.seoStandard.replace('{keyword}', keyword)}",
    "keywords": "${toolConfig.keywordFocus}"
  },
  "placeholderText": "${toolConfig.placeholderTemplate.replace('{keyword}', keyword)}",
  "pageStructure": [${pageStructure.structure.map(s => `"${s}"`).join(', ')}],
  "intro": {
    "description1": "Write a compelling introduction (1-2 paragraphs) matching the style and depth of the few-shot example. Hook the reader, introduce the tool, and include the primary keyword naturally."
  },
  "content": {
    "header": {
      "title": "${keyword}",
      "subtitle": "Create a highly unique subtitle (5-15 words) for '${keyword}'.  First, identify the core essence of '${keyword}' (e.g., for Pop Art: 'vibrant colors, iconic style'; for Cyberpunk: 'neon-drenched futures'). Then, craft a compelling subtitle that weaves these specific themes with the power of 'AI' to highlight a key user benefit. The goal is maximum creativity and style, minimum generic phrasing."
    },
    "sections": {
      "whatIs": {
        "title": "${pageStructure.sectionTitles.whatIs}",
        "description": "In 2-3 detailed paragraphs, explain what this tool is and what makes it special for creating '${keyword}' art. Match the depth and style of the few-shot example."
      },
      "howToUse": {
        "title": "${pageStructure.sectionTitles.howToUse}",
        "subtitle": "A short, encouraging subtitle for the steps. Follow the example style.",
        "steps": [
          ${toolConfig.stepTemplates.step1 ? `{"title": "Step 1: ${toolConfig.stepTemplates.step1.title}", "content": "${toolConfig.stepTemplates.step1.content.replace('{keyword}', keyword)}"},` : ''}
          ${toolConfig.stepTemplates.step2 ? `{"title": "Step 2: ${toolConfig.stepTemplates.step2.title}", "content": "${toolConfig.stepTemplates.step2.content.replace('{keyword}', keyword)}"},` : ''}
          ${toolConfig.stepTemplates.step3 ? `{"title": "Step 3: ${toolConfig.stepTemplates.step3.title}", "content": "${toolConfig.stepTemplates.step3.content.replace('{keyword}', keyword)}"},` : ''}
          ${toolConfig.stepTemplates.step4 ? `{"title": "Step 4: ${toolConfig.stepTemplates.step4.title}", "content": "${toolConfig.stepTemplates.step4.content.replace('{keyword}', keyword)}"}` : ''}
        ]
      },
      "whyUse": {
        "title": "${pageStructure.sectionTitles.whyUse}",
        "subtitle": "A short subtitle explaining the benefits. Match example style.",
        "features": [
          {"title": "Feature 1 Title (specific to ${keyword}) - START WITH A RELEVANT EMOJI", "content": "Feature 1 Description - make it specific and valuable like the example"},
          {"title": "Feature 2 Title (specific to ${keyword}) - START WITH A RELEVANT EMOJI", "content": "Feature 2 Description - match the depth of the example"},
          {"title": "Feature 3 Title (specific to ${keyword}) - START WITH A RELEVANT EMOJI", "content": "Feature 3 Description - follow example quality"},
          {"title": "Feature 4 Title (specific to ${keyword}) - START WITH A RELEVANT EMOJI", "content": "Feature 4 Description - maintain example standards"},
          {"title": "Feature 5 Title (specific to ${keyword}) - START WITH A RELEVANT EMOJI", "content": "Feature 5 Description - maintain example standards"},
          {"title": "Feature 6 Title (specific to ${keyword}) - START WITH A RELEVANT EMOJI", "content": "Feature 6 Description - maintain example standards"}
        ]
      },
      "examples": {
        "title": "${pageStructure.sectionTitles.examples}",
        "description": "A short, exciting paragraph to introduce the gallery of examples. Mention that these were all created with the AI tool. Follow the example tone."
      },
      "applications": {
        "title": "${pageStructure.sectionTitles.applications}",
        "items": [
          {"title": "Use Case 1 Title (specific to ${keyword})", "content": "Use Case 1 Description - match example depth"},
          {"title": "Use Case 2 Title (specific to ${keyword})", "content": "Use Case 2 Description - follow example style"},
          {"title": "Use Case 3 Title (specific to ${keyword})", "content": "Use Case 3 Description - maintain example quality"},
          {"title": "Use Case 4 Title (specific to ${keyword})", "content": "Use Case 4 Description - match example standards"}
        ]
      },
      "tips": {
        "title": "${pageStructure.sectionTitles.tips}",
        "items": [
          "${toolConfig.tips.tip1.replace('{keyword}', keyword)}",
          "${toolConfig.tips.tip2.replace('{keyword}', keyword)}",
          "${toolConfig.tips.tip3.replace('{keyword}', keyword)}"
        ]
      }
    },
    "faq": [
      {"question": "${toolConfig.faq.q1.replace('{keyword}', keyword)}", "answer": "Comprehensive answer matching the depth and style of the few-shot example FAQ"},
      {"question": "${toolConfig.faq.q2.replace('{keyword}', keyword)}", "answer": "Detailed answer with specific tips, following example quality"},
      {"question": "${toolConfig.faq.q3.replace('{keyword}', keyword).replace('{toolName}', toolInfo.name)}", "answer": "Answer about pricing and features, match example style"},
      {"question": "${toolConfig.faq.q4.replace('{keyword}', keyword)}", "answer": "Detailed comparison highlighting advantages, follow example depth"},
      {"question": "${toolConfig.faq.q5.replace('{keyword}', keyword)}", "answer": "Usage rights and applications, match example comprehensiveness"}
    ],
    "cta": {
      "title": "${pageStructure.sectionTitles.cta}",
      "description": "A compelling call-to-action to get the user to try the tool now. Follow the example tone and urgency.",
      "buttonText": "${toolConfig.buttonTemplate.replace('{keyword}', keyword)}"
    }
  },
  "closing": {
    "description": "A final, encouraging paragraph to summarize the tool's value and invite users to start creating. Match the style and enthusiasm of the few-shot example.",
    "buttonText": "${toolConfig.contentType === 'character' ? `Generate Your ${keyword} Character` : toolConfig.contentType === 'comic' ? `Generate Your ${keyword} Comic` : `Generate Your ${keyword} Masterpiece`}"
  }
}

**6. Original Tool Content (Additional Reference):**
${JSON.stringify(baseContent, null, 2)}

**FINAL REMINDER**:
- Use the few-shot example as your primary quality benchmark. Your output should match its depth, professionalism, and user value while being specifically tailored to "${keyword}".
- Use the functional description template to ensure content accurately reflects actual tool functionality rather than AI speculation.
- **IMPORTANT**: For the SEO title field, output EXACTLY "${keyword}" - no additional text, suffixes, or modifications.
- **IMPORTANT**: For the subtitle field, focus only on "${keyword}" - do NOT include "${toolInfo.name}" or any tool references.

Generate the content now:`
}

// 专门用于SEO-only模式的内容生成
async function generateSeoOnlyContent(toolType, keyword, config) {
  const TOOL_TYPES = getSupportedToolTypes()
  const toolInfo = TOOL_TYPES[toolType]
  if (!toolInfo) {
    throw new Error(`不支持的工具类型: ${toolType}`)
  }

  // 加载few-shot example
  let fewShotExample = null
  try {
    const examplePath = path.join(
      __dirname,
      `../../src/i18n/locales/en/${toolType}.json`,
    )
    if (fs.existsSync(examplePath)) {
      fewShotExample = JSON.parse(fs.readFileSync(examplePath, 'utf8'))
    }
  } catch (error) {
    console.warn(`⚠️ 无法加载few-shot示例: ${error.message}`)
  }

  const toolConfig = getToolTypeConfig(toolType)

  const seoOnlyPrompt = `
You are an SEO expert specializing in creating optimized meta content. Your task is to generate ONLY SEO metadata for a "${keyword}" page.

${
  fewShotExample
    ? `
**Few-Shot SEO Example:**
Reference this high-quality SEO structure from the original tool:
- Title: "${fewShotExample.title || fewShotExample.meta?.title || ''}"
- Description: "${fewShotExample.meta?.description || fewShotExample.header?.subtitle || ''}"
- Keywords: "${fewShotExample.meta?.keywords || ''}"
`
    : ''
}

**Requirements:**
- **CRITICAL**: Title must be EXACTLY "${keyword}" - no additional text
- **CRITICAL**: Description should be SEO-optimized meta description, under 160 characters, include "${keyword}" naturally with a call-to-action, focus on search engine ranking
- **CRITICAL**: Keywords should include "${keyword}" and related terms, 10-15 keywords total
- **CRITICAL**: Subtitle should be user-facing, engaging copy that explains the value proposition clearly without mentioning tool names

**Target Keywords:** "${keyword}"
**Tool Context:** This is for a ${toolInfo.name} specialized for "${keyword}" ${toolConfig.description}.

**Functional Description Template:**
Use this to understand what the tool actually does:
"${toolConfig.functionalDescription.replace('{keyword}', keyword)}"

**Output ONLY a JSON object with SEO data and subtitle:**
{
  "title": "${keyword}",
  "description": "Craft a meta description (150-160 characters) for '${keyword}'. It must be a clear, compelling summary reflecting user search intent. Start by stating what the tool does, highlight a benefit, and end with a strong call-to-action. Integrate the primary keyword naturally. ${toolConfig.seoStandard.replace('{keyword}', keyword)}",
  "keywords": "${toolConfig.keywordFocus}",
  "subtitle": "Create a highly unique subtitle (5-15 words) for '${keyword}'. **CRITICAL: Do NOT use the phrase 'Transform your ideas' or any similar variation.** First, identify the core essence of '${keyword}' (e.g., ${
    toolConfig.contentType === 'character'
      ? `for Naruto: 'ninja powers, hidden villages'; for Pokemon: 'creature companions, adventures'`
      : toolConfig.contentType === 'comic'
        ? `for Manga: 'Japanese storytelling, dynamic panels'; for Horror: 'spine-chilling narratives, dark atmosphere'`
        : `for Pop Art: 'vibrant colors, iconic style'; for Cyberpunk: 'neon-drenched futures'`
  }). Then, craft a compelling subtitle that weaves these specific themes with the power of 'AI' to highlight a key user benefit. The goal is maximum creativity and style, minimum generic phrasing."
}

Generate the SEO content now:`

  console.log('🤖 正在生成SEO内容...')

  try {
    const response = await fetch(`${config.apiBaseUrl}/api/generateText`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: `next-auth.session-token=${config.sessionToken}`,
      },
      body: JSON.stringify({
        prompt: seoOnlyPrompt,
        noNeedLogin: true,
      }),
    })

    if (!response.ok) {
      throw new Error(`SEO内容生成失败: ${response.status}`)
    }

    const seoResponse = await response.json()

    // 解析AI返回的JSON
    const jsonMatch = seoResponse.match(/\{[\s\S]*?\}/)
    if (!jsonMatch) {
      throw new Error('AI返回格式不正确，未找到SEO JSON')
    }

    const seoContent = JSON.parse(jsonMatch[0])

    // 验证SEO内容
    if (
      !seoContent.title ||
      !seoContent.description ||
      !seoContent.keywords ||
      !seoContent.subtitle
    ) {
      throw new Error(
        'SEO内容缺少必需字段（title, description, keywords, subtitle）',
      )
    }

    console.log('✅ SEO内容生成完成')
    return seoContent
  } catch (error) {
    console.error('❌ SEO内容生成失败:', error.message)
    throw error
  }
}

// AI自我反思和矫正功能
async function performSelfCriticism(content, keyword, config) {
  const criticismPrompt = `
You are a quality assurance expert reviewing AI-generated content for a "${keyword}" page. Your task is to perform self-criticism and provide improvements.

**Original Content to Review:**
\`\`\`json
${JSON.stringify(content, null, 2)}
\`\`\`

**Review Criteria:**
1. **Title Issues**: SEO title should be EXACTLY "${keyword}" - no additional text
2. **Description Issues**: Is the meta description 150-160 characters? Does it clearly summarize the page, highlight a key benefit, and include a strong call-to-action? It must be compelling and align with user search intent for "${keyword}".
3. **Subtitle Issues**: Is the subtitle creative and unique? Does it completely avoid the 'Transform ideas' formula? Does it weave the core themes of "${keyword}" with the mention of 'AI' to create a compelling, non-generic message?
4. **Content Quality**: Check for generic content that could be improved with ${keyword}-specific details
5. **Keywords**: Focus on AI-powered search intent. Include 'AI' in 2-3 key terms. Use genuine search terms like 'AI anime generator', 'AI character creator'. Prioritize real search queries over keyword stuffing (5-8 keywords max).
6. **Consistency**: All content should be consistently focused on ${keyword}
7. **Value Proposition**: Each section should provide specific value for ${keyword} users
8. **Using Natural Language**: Use natural language and avoid keyword stuffing

**Self-Criticism Instructions:**
1. First, identify specific issues in the current content
2. Then provide the corrected JSON with improvements
3. Focus on making content more specific to "${keyword}" and removing generic language

**Output Format:**
First provide your criticism analysis, then output the improved JSON:

CRITICISM:
- Issue 1: [describe specific problem]
- Issue 2: [describe specific problem]
- etc.

IMPROVED CONTENT:
\`\`\`json
{corrected json here}
\`\`\`

Begin your review:`

  try {
    const response = await fetch(`${config.apiBaseUrl}/api/generateText`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: `next-auth.session-token=${config.sessionToken}`,
      },
      body: JSON.stringify({
        prompt: criticismPrompt,
        noNeedLogin: true,
      }),
    })

    if (!response.ok) {
      console.warn('⚠️ 自我反思步骤失败，使用原始内容')
      return content
    }

    const criticismResponse = await response.json()

    // 提取改进后的JSON
    const improvedJsonMatch = criticismResponse.match(
      /```json\s*(\{[\s\S]*?\})\s*```/,
    )
    if (improvedJsonMatch) {
      try {
        const improvedContent = JSON.parse(improvedJsonMatch[1])
        console.log('✅ AI自我反思完成，内容已优化')

        // 保留原始的pageStructure如果新版本没有
        if (!improvedContent.pageStructure && content.pageStructure) {
          improvedContent.pageStructure = content.pageStructure
        }

        return improvedContent
      } catch (parseError) {
        console.warn('⚠️ 无法解析改进后的内容，使用原始内容')
        return content
      }
    } else {
      console.warn('⚠️ 未找到改进后的JSON，使用原始内容')
      return content
    }
  } catch (error) {
    console.warn('⚠️ 自我反思过程出错，使用原始内容:', error.message)
    return content
  }
}

// 调用AI生成页面内容
async function generatePageContent(toolType, keyword, config) {
  const TOOL_TYPES = getSupportedToolTypes()
  const toolInfo = TOOL_TYPES[toolType]
  if (!toolInfo) {
    throw new Error(`不支持的工具类型: ${toolType}`)
  }

  const prompt = generateContentPrompt(toolType, keyword, toolInfo)

  console.log('🤖 正在生成页面内容...')

  let response
  try {
    response = await fetch(`${config.apiBaseUrl}/api/generateText`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: `next-auth.session-token=${config.sessionToken}`,
      },
      body: JSON.stringify({
        prompt: prompt,
        noNeedLogin: true,
      }),
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('API错误响应:', errorText)
      throw new Error(`内容生成失败: ${response.status} - ${errorText}`)
    }
  } catch (error) {
    if (error.code === 'ENOTFOUND' || error.message.includes('fetch failed')) {
      throw new Error(
        '网络连接失败，请检查网络连接或稍后重试。如果问题持续，请检查sessionToken是否正确设置。',
      )
    }
    throw error
  }

  const aiResponse = await response.json()

  // aiResponse 是直接的文本内容
  if (!aiResponse) {
    throw new Error('AI未返回内容')
  }

  // 解析AI返回的JSON
  const jsonMatch = aiResponse.match(/\{[\s\S]*\}/)
  if (!jsonMatch) {
    throw new Error('AI返回格式不正确，未找到JSON')
  }

  const content = JSON.parse(jsonMatch[0])

  // 确保pageStructure存在
  if (!content.pageStructure) {
    console.warn('⚠️ AI未返回pageStructure，使用默认结构')
    content.pageStructure = [
      'examples',
      'whatIs',
      'howToUse',
      'whyUse',
      'applications',
      'tips',
      'faq',
      'cta',
    ]
  }

  console.log('✅ 页面内容生成完成')
  console.log(`📋 使用页面结构: ${content.pageStructure.join(' → ')}`)

  // 添加自我反思和矫正步骤
  console.log('🔍 开始AI自我反思和矫正...')
  const improvedContent = await performSelfCriticism(
    content,
    keyword,
    config,
  )

  return improvedContent
}

















// 主函数 - 一键生成衍生页面
async function generateVariantPage(toolType, keyword, options = {}) {
  console.log(`\n🚀 开始生成衍生页面: ${toolType}/${keyword}`)

  // 检查页面是否已存在
  const existingPage = checkExistingPage(toolType, keyword)

  if (existingPage.exists) {
    console.log(`📄 页面已存在`)
    console.log(`📝 内容: ${existingPage.hasContent ? '✅ 已有' : '❌ 缺失'}`)
    console.log(
      `🖼️  图片: ${
        existingPage.hasImages ? `✅ ${existingPage.imageCount}张` : '❌ 缺失'
      }`,
    )

    // SEO-only模式：只重新生成SEO内容
    if (options.seoOnly) {
      if (!existingPage.hasContent) {
        throw new Error('❌ SEO-only模式需要页面已有内容，但当前页面内容缺失')
      }
      console.log('🔍 SEO-only模式：只重新生成SEO内容')
    }
    // 如果内容和图片都存在，直接跳过（除非是强制模式或只生成图片模式或SEO模式）
    else if (
      existingPage.hasContent &&
      existingPage.hasImages &&
      !options.force &&
      !options.imagesOnly
    ) {
      console.log(`✅ 页面完整，跳过生成`)
      console.log(`📄 页面: /tools/${toolType}/${keywordToSlug(keyword)}`)
      console.log(`🖼️  图片: ${existingPage.imageCount} 张`)
      return {
        skipped: true,
        reason: 'Page already exists with content and images',
        path: `/tools/${toolType}/${keywordToSlug(keyword)}`,
        imageCount: existingPage.imageCount,
      }
    }

    // 如果只缺少图片，只生成图片
    if (
      existingPage.hasContent &&
      !existingPage.hasImages &&
      !options.textOnly &&
      !options.seoOnly
    ) {
      console.log(`📝 内容已存在，只生成图片`)
      options.contentOnly = false
      options.imagesOnly = true
    }

    // 如果只缺少内容，只生成内容
    if (
      !existingPage.hasContent &&
      existingPage.hasImages &&
      !options.seoOnly
    ) {
      console.log(`🖼️  图片已存在，只生成内容`)
      options.contentOnly = true
      options.imagesOnly = false
    }
  } else if (options.seoOnly) {
    throw new Error('❌ SEO-only模式需要页面已存在，但未找到现有页面')
  }

  // 加载配置
  const config = await setupConfig()

  console.log(
    `📊 配置: 模型=${
      options.model || config.defaultModel || ConfigManager.DEFAULT_CONFIG.defaultModel
    }, 图片数量=${
      options.count ||
      Math.ceil(config.imagesPerVariant / Object.keys(IMAGE_RATIOS).length)
    }`,
  )

  if (!config.sessionToken && !options.skipApiCall) {
    throw new Error(
      '需要设置 sessionToken，请先运行: node generate-variant-page.mjs setup',
    )
  }

  const model =
    options.model || config.defaultModel || ConfigManager.DEFAULT_CONFIG.defaultModel
  const count = parseInt(options.count) || 8 // 默认10个不同的prompts，对应30张图片
  const textOnly = options.textOnly || false

  try {
    let pageContent = null

    // SEO-only模式：只重新生成SEO内容
    if (options.seoOnly) {
      console.log('🔍 SEO-only模式：重新生成SEO内容')

      // 使用现有页面内容作为基础
      pageContent = { ...existingPage.data }

      // 生成新的SEO内容（专门的SEO-only函数）
      const newSeoContent = await generateSeoOnlyContent(
        toolType,
        keyword,
        config,
      )

      // 更新SEO和subtitle，保留其他所有内容（包括examples）
      pageContent.seo = {
        title: newSeoContent.title,
        description: newSeoContent.description,
        keywords: newSeoContent.keywords,
      }

      // 更新header中的subtitle
      if (!pageContent.content) {
        pageContent.content = {}
      }
      if (!pageContent.content.header) {
        pageContent.content.header = {}
      }
      pageContent.content.header.subtitle = newSeoContent.subtitle

      console.log('✅ SEO内容和subtitle已更新')
      console.log(
        `📊 新SEO内容: 标题="${pageContent.seo.title}", 描述="${pageContent.seo.description}"`,
      )
      console.log(`📝 新subtitle: "${pageContent.content.header.subtitle}"`)

      // 保存更新后的内容，明确保留原有examples
      const originalExamples = pageContent.content?.examples || []
      updateVariantPages(toolType, keyword, pageContent, '')

      console.log(`\n🎉 SEO内容和subtitle更新完成!`)
      console.log(`📄 页面: /tools/${toolType}/${keywordToSlug(keyword)}`)
      console.log(`🖼️  保留原有图片: ${originalExamples.length} 张`)
      return {
        success: true,
        mode: 'seo-only',
        path: `/tools/${toolType}/${keywordToSlug(keyword)}`,
        seo: pageContent.seo,
        subtitle: pageContent.content.header.subtitle,
        preservedImages: originalExamples.length,
      }
    }

    // 1. 生成或使用现有页面内容
    if (options.imagesOnly && existingPage.exists && existingPage.hasContent) {
      console.log('📝 使用现有页面内容')
      pageContent = existingPage.data
    } else if (!options.imagesOnly) {
      console.log('📝 生成页面内容')
      pageContent = await generatePageContent(toolType, keyword, config)
    } else if (options.imagesOnly) {
      // images-only模式但没有现有内容，需要生成基础内容结构
      console.log('📝 images-only模式：生成基础页面内容')
      pageContent = await generatePageContent(toolType, keyword, config)
    }

    // 2. 先保存页面内容（不含图片）
    if (pageContent && !options.imagesOnly) {
      console.log('💾 先保存页面内容...')
      updateVariantPages(toolType, keyword, pageContent, [])
      console.log('✅ 页面内容已保存')
    }

    let examples = []
    let prompts = []

    // 3. 处理图片生成
    if (options.contentOnly) {
      console.log('📝 只生成内容模式，跳过图片生成')
      // 使用现有图片
      if (existingPage.exists && existingPage.hasImages) {
        examples = existingPage.data.content?.examples || []
      }
    } else if (getToolTypeConfig(toolType).skipImages) {
      console.log(`📝 ${toolType}类型，跳过图片生成`)
      // 使用现有图片如果存在
      if (existingPage.exists && existingPage.hasImages) {
        examples = existingPage.data.content?.examples || []
      }
    } else if (!textOnly && !options.imagesOnly) {
      // 正常模式：生成图片
      if (toolType === 'playground') {
        // 对于playground类型，直接生成图片，不需要AI生成prompts
        console.log('🖼️  直接生成playground图片（跳过prompt生成）')
        examples = await generateImages([], keyword, model, config, toolType)
      } else {
        console.log('🖼️  生成图片prompts')
        prompts = await generateImagePrompts(
          toolType,
          keyword,
          pageContent,
          config,
          count,
        )

        console.log('🖼️  生成图片')
        examples = await generateImages(prompts, keyword, model, config, toolType)
      }
    } else if (options.imagesOnly) {
      // 只生成图片模式
      if (toolType === 'playground') {
        // 对于playground类型，直接生成图片，不需要AI生成prompts
        console.log('🖼️  只生成playground图片模式（跳过prompt生成）')
        examples = await generateImages([], keyword, model, config, toolType)
      } else {
        console.log('🖼️  只生成图片模式')
        prompts = await generateImagePrompts(
          toolType,
          keyword,
          pageContent,
          config,
          count,
        )

        examples = await generateImages(prompts, keyword, model, config, toolType)
      }
    } else {
      console.log('📝 只生成文案模式，跳过图片生成')
      // 使用现有图片如果存在
      if (existingPage.exists && existingPage.hasImages) {
        examples = existingPage.data.content?.examples || []
      }
    }

    // 4. 更新variant数据（添加图片）
    if (examples.length > 0) {
      console.log('🔄 更新页面数据，添加图片...')
      updateVariantPages(toolType, keyword, pageContent, examples)
      console.log('✅ 图片数据已更新')
    }

    console.log(`\n🎉 衍生页面生成完成!`)
    console.log(`📄 页面: /tools/${toolType}/${keywordToSlug(keyword)}`)
    if (!textOnly) {
      console.log(`🖼️  图片: ${examples.length} 张`)
    } else {
      console.log(`📝 只生成文案，未生成图片`)
    }
  } catch (error) {
    console.error('❌ 生成失败:', error.message)
    throw error
  }
}

// 解析命令行参数
function parseArgs() {
  const args = process.argv.slice(2)

  if (args.length === 0) {
    console.log(
      '用法: node generate-variant-page.mjs <tool-type> <keyword> [options]',
    )
    console.log('选项:')
    console.log(
      '  --count=N        生成N个提示词 (默认: 10, 总图片数 = N×3种比例)',
    )
    console.log('  --model=MODEL    使用指定模型 (默认: AnimagineXL)')
    console.log('  --text-only      只生成文案，不生成图片')
    console.log('  --images-only    只生成图片，跳过内容检测')
    console.log('  --seo-only       只重新生成SEO内容，保留其他内容')
    console.log('  --force          强制重新生成，即使页面已存在')
    console.log('  setup           设置API tokens')
    process.exit(1)
  }

  if (args[0] === 'setup') {
    return { action: 'setup' }
  }

  const toolType = args[0]
  const keyword = args[1]

  if (!toolType || !keyword) {
    console.error('❌ 错误: 需要提供工具类型和关键词')
    process.exit(1)
  }

  let promptCount = 8 // 默认10个提示词，对应30张图片
  let model = null // 改为null，让config.defaultModel生效
  let textOnly = false
  let imagesOnly = false
  let seoOnly = false
  let force = false
  let skipApiCall = false

  // 解析选项
  for (let i = 2; i < args.length; i++) {
    const arg = args[i]
    if (arg.startsWith('--count=')) {
      promptCount = parseInt(arg.split('=')[1]) || 10
    } else if (arg.startsWith('--model=')) {
      model = arg.split('=')[1] || 'Gemini'
    } else if (arg === '--text-only') {
      textOnly = true
    } else if (arg === '--images-only') {
      imagesOnly = true
    } else if (arg === '--seo-only') {
      seoOnly = true
    } else if (arg === '--force') {
      force = true
    } else if (arg === '--skip-api-call') {
      skipApiCall = true
    }
  }

  return {
    action: 'generate',
    toolType,
    keyword,
    promptCount,
    model,
    textOnly,
    imagesOnly,
    seoOnly,
    force,
    skipApiCall,
  }
}

// 主执行逻辑
async function main() {
  try {
    const args = parseArgs()

    if (args.action === 'setup') {
      await setupConfig()
      return
    }

    if (args.action === 'generate') {
      // generateVariantPage 函数内部会加载配置，所以这里不需要预先加载
      await generateVariantPage(args.toolType, args.keyword, {
        count: args.promptCount,
        model: args.model, // 直接传递，让 generateVariantPage 处理默认值
        textOnly: args.textOnly,
        imagesOnly: args.imagesOnly,
        seoOnly: args.seoOnly,
        force: args.force,
        skipApiCall: args.skipApiCall,
      })
    }
  } catch (error) {
    console.error('❌ 执行失败:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此脚本，执行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}
