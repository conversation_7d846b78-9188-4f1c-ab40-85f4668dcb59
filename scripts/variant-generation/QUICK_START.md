# 🚀 快速入门指南

5分钟内开始使用衍生页面生成系统！

## 第一步：环境准备

```bash
# 进入项目目录
cd scripts/variant-generation

# 确保Node.js版本 >= 14
node --version
```

## 第二步：首次配置

运行任意命令，系统会自动引导配置：

```bash
node generate-variant-page.mjs
```

按提示输入：

- **API地址**: `http://localhost:3000` (默认)
- **会话令牌**: 留空 (可选)
- **AI模型**: `Animagine` (默认)
- **图片数量**: `6` (默认)

## 第三步：生成第一个页面

### 🎨 AI动漫生成器

```bash
node generate-variant-page.mjs ai-anime-generator "Genshin Impact Character Generator"
```

### 👤 原创角色制作器

```bash
node generate-variant-page.mjs oc-maker "Pokemon OC Maker"
```

### 📚 AI漫画生成器

```bash
node generate-variant-page.mjs ai-comic-generator "Superhero Comic Creator"
```

### 🎭 风格转换器

```bash
node generate-variant-page.mjs playground "Anime Style Transfer"
```

## 第四步：查看结果

生成的文件位于：

```
public/tools/[工具类型]/[页面名称]/[页面名称].json
```

例如：

```
public/tools/ai-anime-generator/genshin-impact-character-generator/genshin-impact-character-generator.json
```

## 🎯 常用命令

### 只生成文案（快速测试）

```bash
node generate-variant-page.mjs ai-anime-generator "Test Page" --text-only
```

### 生成更多图片

```bash
node generate-variant-page.mjs oc-maker "Character Creator" --images=12
```

### 测试模式（不调用API）

```bash
node generate-variant-page.mjs playground "Style Transfer" --skip-api-call
```

## 🔧 快速故障排除

### 问题1：API连接失败

```bash
# 检查配置
cat config.json

# 重新配置
rm config.json
node generate-variant-page.mjs
```

### 问题2：生成失败

```bash
# 使用测试模式
node generate-variant-page.mjs ai-anime-generator "Test" --skip-api-call --text-only
```

### 问题3：图片生成慢

```bash
# 先生成文案
node generate-variant-page.mjs oc-maker "Character" --text-only

# 再单独生成图片（如需要）
```

## 📊 成功指标

生成成功后，你应该看到：

```
✅ 配置文件已存在
✅ 成功加载 [工具类型] 的基础内容
✅ 加载few-shot示例
✅ AI自我反思完成，内容已优化
✅ [工具类型]/[页面名称].json 已更新
🎉 衍生页面生成完成!
```

## 🎓 进阶使用

### 批量生成

```bash
# 使用专门的批量脚本
node generate-oc-maker-variants.mjs
```

### 验证系统

```bash
# 验证示例文件
node validate-examples.mjs

# 测试所有工具配置
node test-tool-configs.mjs
```

### 自定义配置

编辑 `tool-configs/[工具类型]/config.mjs` 来自定义：

- 页面模板
- 按钮文本
- SEO设置
- 功能描述
