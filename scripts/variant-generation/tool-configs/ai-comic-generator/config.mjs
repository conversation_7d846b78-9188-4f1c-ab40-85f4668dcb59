/**
 * AI Comic Generator 工具配置
 */

export const AI_COMIC_GENERATOR_CONFIG = {
  name: 'AI Comic Generator',
  description: 'comic creation',
  targetAudience: 'Comic creators, storytellers, writers, and content creators interested in creating comics, graphic novels, and visual narratives',
  contentType: 'comic',
  placeholderTemplate: 'A thrilling {keyword} adventure story with heroes and villains',
  buttonTemplate: 'Create {keyword} Comics Free',
  functionalDescription: 'AI {keyword} Generator: an AI tool that creates {keyword} style comics from story descriptions. First input your {keyword} story plot and characters, then select the {keyword} comic style from various templates, then click Generate Comic, and you will get a complete {keyword} style comic sequence.',
  stepTemplates: {
    step1: 'Write your {keyword} story plot and character descriptions',
    step2: 'Choose your preferred {keyword} comic art style',
    step3: 'Click Generate to create your {keyword} comic',
    step4: 'Download and share your {keyword} comic creation'
  },
  seoStandard: 'Follow this quality standard for comic creation: \'Create amazing {keyword} comics with AI. Generate engaging visual stories and narratives instantly. Try it free!\'',
  keywordFocus: '5-8 core keywords focusing on AI-powered comic creation. Include \'AI\' in 2-3 key terms since users specifically search for AI tools. Primary keyword once, then add genuine search terms like \'AI comic generator\', \'comic creator\', \'comic maker\', plus relevant non-AI terms users actually type. Prioritize real search intent over keyword stuffing.',
  tips: {
    tip1: 'Tip 1 for \'{keyword}\' comic creation - make it specific and actionable like the example tips.',
    tip2: 'Tip 2 for \'{keyword}\' comic storytelling - follow the format and depth of the example.',
    tip3: 'Tip 3 for \'{keyword}\' visual narrative - match the practical value of the example tips.'
  },
  faq: {
    q1: 'What is {keyword} comic generation and how does it work?',
    q2: 'How to create the best {keyword} comics with AI?',
    q3: 'Is {keyword} comic generation free to use?',
    q4: 'What makes {keyword} comics unique compared to other styles?',
    q5: 'Can I use {keyword} comics commercially?'
  },
  skipImages: false,
  ratioOverride: null
}

export default AI_COMIC_GENERATOR_CONFIG
