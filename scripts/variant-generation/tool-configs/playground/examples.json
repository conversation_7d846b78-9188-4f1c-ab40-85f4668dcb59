{"fewShotExamples": [{"keyword": "ai-cosplay-generator", "examples": [{"input": "/images/examples/playground/ai-cosplay-generator/genshin-input.webp", "image": "/images/examples/playground/ai-cosplay-generator/genshin-output.webp", "prompt": "Style: Cosplay"}, {"input": "/images/examples/playground/ai-cosplay-generator/doma-input.webp", "image": "/images/examples/playground/ai-cosplay-generator/doma-output.webp", "prompt": "Style: Cosplay"}, {"input": "/images/examples/playground/ai-cosplay-generator/naruto-input.webp", "image": "/images/examples/playground/ai-cosplay-generator/naruto-output.webp", "prompt": "Style: Cosplay"}]}, {"keyword": "ai-plush-generator", "examples": [{"input": "/images/examples/playground/ai-plush-generator/genshin-input.webp", "image": "/images/examples/playground/ai-plush-generator/genshin-output.webp", "prompt": "Style: <PERSON><PERSON><PERSON>"}, {"input": "/images/examples/playground/ai-plush-generator/Saber-input.webp", "image": "/images/examples/playground/ai-plush-generator/Saber-output.webp", "prompt": "Style: <PERSON><PERSON><PERSON>"}, {"input": "/images/examples/playground/ai-plush-generator/girl-input.webp", "image": "/images/examples/playground/ai-plush-generator/girl-output.webp", "prompt": "Style: <PERSON><PERSON><PERSON>"}]}, {"keyword": "ai-emoji-generator", "examples": [{"image": "/images/examples/playground/ai-emoji-generator/example_1.webp", "prompt": "Style: <PERSON><PERSON>", "input": "/images/examples/photo-to-anime/input2.jpg"}, {"image": "/images/examples/playground/ai-emoji-generator/example_2.webp", "prompt": "Style: <PERSON><PERSON>", "input": "/images/examples/photo-to-anime/black_guy_photo.webp"}, {"image": "/images/examples/playground/ai-emoji-generator/example_3.webp", "prompt": "Style: <PERSON><PERSON>", "input": "/images/examples/photo-to-anime/cat_photo.webp"}]}, {"keyword": "photo-to-minecraft", "examples": [{"image": "/images/examples/playground/photo-to-minecraft/example_1.webp", "prompt": "Style: Pixel Art", "input": "/images/examples/photo-to-anime/input2.jpg"}, {"image": "/images/examples/playground/photo-to-minecraft/example_2.webp", "prompt": "Style: Pixel Art", "input": "/images/examples/photo-to-anime/black_guy_photo.webp"}]}], "styleTemplates": ["Style: {style_name}", "Transform to {style_name} style", "Convert to {style_name}", "Apply {style_name} filter", "{style_name} transformation"], "supportedStyles": ["Cosplay", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Pixel Art", "Sprite Sheet", "Anime Style", "Manga Style", "Cartoon Style", "Watercolor", "Oil Painting", "Sketch Style", "Digital Art"], "inputTypes": ["photo", "character art", "portrait", "illustration", "3D render", "sketch", "digital art"], "transformationFeatures": ["style transfer", "artistic filter", "character transformation", "visual style conversion", "artistic enhancement", "creative transformation"]}