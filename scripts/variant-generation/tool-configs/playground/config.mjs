/**
 * Playground (AI Style Transfer) 工具配置
 */

export const PLAYGROUND_CONFIG = {
  name: 'AI Style Transfer',
  description: 'style transformation',
  targetAudience: 'Artists, designers, content creators, and hobbyists interested in transforming photos into different artistic styles',
  contentType: 'style',
  placeholderTemplate: 'Transform your photo into {keyword} style',
  buttonTemplate: 'Transform to {keyword} Free',
  functionalDescription: 'AI {keyword} Generator: an AI tool that converts your photos to {keyword} style. First upload your photo or image, then select the {keyword} template, then click Convert to {keyword}, and you will get a photo transformed into {keyword} style.',
  stepTemplates: {
    step1: 'Upload your photo or image',
    step2: 'Select the {keyword} style template',
    step3: 'Click Convert to transform to {keyword} style',
    step4: 'Download your {keyword} style creation'
  },
  seoStandard: 'Follow this quality standard for style transformation: \'Transform photos to {keyword} style with AI. Convert images into artistic {keyword} effects instantly. Try it free!\'',
  keywordFocus: '5-8 core keywords focusing on AI-powered style transformation. Include \'AI\' in 2-3 key terms since users specifically search for AI tools. Primary keyword once, then add genuine search terms like \'AI style transfer\', \'photo filter\', \'style converter\', plus relevant non-AI terms users actually type. Prioritize real search intent over keyword stuffing.',
  tips: {
    tip1: 'Tip 1 for \'{keyword}\' style transformation - make it specific and actionable like the example tips.',
    tip2: 'Tip 2 for \'{keyword}\' style creation - follow the format and depth of the example.',
    tip3: 'Tip 3 for \'{keyword}\' artistic effects - match the practical value of the example tips.'
  },
  faq: {
    q1: 'What is {keyword} style transformation and how does it work?',
    q2: 'How to create the best {keyword} style effects with AI?',
    q3: 'Is {keyword} style transformation free to use?',
    q4: 'What makes {keyword} style unique compared to other filters?',
    q5: 'Can I use {keyword} style creations commercially?'
  },
  skipImages: false,
  ratioOverride: null
}

export default PLAYGROUND_CONFIG
