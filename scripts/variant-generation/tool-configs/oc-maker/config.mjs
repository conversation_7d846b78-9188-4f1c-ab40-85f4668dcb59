/**
 * OC Maker 工具配置
 */

export const OC_MAKER_CONFIG = {
  name: 'OC Maker',
  description: 'character creation',
  targetAudience: 'Artists, writers, game developers, and content creators interested in creating original characters (OCs) for their stories, games, comics, and animations',
  contentType: 'character',
  placeholderTemplate: 'A {keyword} character with blue hair and magical powers',
  buttonTemplate: 'Create {keyword} Characters Free',
  functionalDescription: 'AI {keyword} Generator: an AI tool that creates {keyword} style characters from text descriptions. First input your character description including {keyword} traits and appearance, then select the {keyword} character style from various templates, then click Generate Character, and you will get a unique {keyword} style character design.',
  stepTemplates: {
    step1: 'Describe your {keyword} character\'s appearance and traits',
    step2: 'Choose your preferred {keyword} character style',
    step3: 'Click Generate to create your {keyword} character',
    step4: 'Download and use your {keyword} character design'
  },
  seoStandard: 'Follow this quality standard for character creation: \'Create unique {keyword} characters with AI. Design original characters with custom appearances and personalities. Try it free!\'',
  keywordFocus: '5-8 core keywords focusing on AI-powered character creation. Include \'AI\' in 2-3 key terms since users specifically search for AI tools. Primary keyword once, then add genuine search terms like \'AI character creator\', \'character generator\', \'OC maker\', plus relevant non-AI terms users actually type. Prioritize real search intent over keyword stuffing.',
  tips: {
    tip1: 'Tip 1 for \'{keyword}\' character design - make it specific and actionable like the example tips.',
    tip2: 'Tip 2 for \'{keyword}\' character design - follow the format and depth of the example.',
    tip3: 'Tip 3 for \'{keyword}\' character development - match the practical value of the example tips.'
  },
  faq: {
    q1: 'What is {keyword} OC Maker and how does it work?',
    q2: 'How to create the best {keyword} characters with AI?',
    q3: 'Is {keyword} OC Maker free to use?',
    q4: 'What makes {keyword} characters unique?',
    q5: 'Can I use {keyword} characters commercially?'
  },
  skipImages: false,
  ratioOverride: 'portrait-2-3'
}

export default OC_MAKER_CONFIG
